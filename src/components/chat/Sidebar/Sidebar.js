import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import './Sidebar.css';

const Sidebar = ({ isOpen, onToggle }) => {
  const [activeItem, setActiveItem] = useState('new-chat');
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const menuItems = [
    { id: 'new-chat', label: 'New chat', icon: '💬' },
    { id: 'projects', label: 'Projects', icon: '📁' },
    { id: 'search', label: 'Search', icon: '🔍' }
  ];

  const bottomItems = [
    { id: 'settings', label: 'Settings', icon: '⚙️' }
  ];

  const handleItemClick = (itemId) => {
    setActiveItem(itemId);

    if (itemId === 'settings') {
      navigate('/profile');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const getUserInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    } else if (user?.email) {
      return user.email[0].toUpperCase();
    }
    return 'U';
  };

  const getUserName = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    } else if (user?.firstName) {
      return user.firstName;
    } else if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'User';
  };

  const getUserPlan = () => {
    return user?.plan || 'Free Plan';
  };

  return (
    <div className={`sidebar ${isOpen ? 'sidebar--open' : 'sidebar--closed'}`}>
      {/* Header */}
      <div className="sidebar__header">
        <div className="sidebar__logo">
          <h2>TheInfini AI</h2>
        </div>
        <button className="sidebar__toggle" onClick={onToggle}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>

      {/* New Chat Button */}
      <div className="sidebar__new-chat">
        <button className="sidebar__new-chat-btn">
          <span className="sidebar__new-chat-icon">+</span>
          {isOpen && <span>New chat</span>}
        </button>
      </div>

      {/* Main Menu Items */}
      <nav className="sidebar__nav">
        <ul className="sidebar__nav-list">
          {menuItems.map((item) => (
            <li key={item.id}>
              <button
                className={`sidebar__nav-item ${activeItem === item.id ? 'sidebar__nav-item--active' : ''}`}
                onClick={() => handleItemClick(item.id)}
              >
                <span className="sidebar__nav-icon">{item.icon}</span>
                {isOpen && <span className="sidebar__nav-label">{item.label}</span>}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Bottom Section */}
      <div className="sidebar__bottom">
        {/* Settings */}
        {bottomItems.map((item) => (
          <button
            key={item.id}
            className={`sidebar__nav-item ${activeItem === item.id ? 'sidebar__nav-item--active' : ''}`}
            onClick={() => handleItemClick(item.id)}
          >
            <span className="sidebar__nav-icon">{item.icon}</span>
            {isOpen && <span className="sidebar__nav-label">{item.label}</span>}
          </button>
        ))}

        {/* User Profile */}
        <div className="sidebar__user-profile" onClick={() => navigate('/profile')}>
          <div className="sidebar__user-avatar">
            {user?.profilePicture ? (
              <img
                src={user.profilePicture}
                alt="Profile"
                className="sidebar__user-avatar-image"
              />
            ) : (
              <div className="sidebar__user-avatar-placeholder">
                {getUserInitials()}
              </div>
            )}
          </div>
          {isOpen && (
            <div className="sidebar__user-info">
              <div className="sidebar__user-name">{getUserName()}</div>
              <div className="sidebar__user-plan">{getUserPlan()}</div>
            </div>
          )}
          {isOpen && (
            <button
              className="sidebar__logout-btn"
              onClick={(e) => {
                e.stopPropagation();
                handleLogout();
              }}
              title="Logout"
            >
              🚪
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
