.sidebar {
  background-color: var(--secondary-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  min-height: 100vh;
}

.sidebar--open {
  width: 280px;
}

.sidebar--closed {
  width: 60px;
}

/* Header */
.sidebar__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.sidebar__logo h2 {
  color: var(--accent-color);
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar--closed .sidebar__logo h2 {
  display: none;
}

.sidebar__toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar__toggle:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

/* New Chat Button */
.sidebar__new-chat {
  padding: 16px;
}

.sidebar__new-chat-btn {
  width: 100%;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.sidebar--closed .sidebar__new-chat-btn {
  padding: 12px;
  justify-content: center;
}

.sidebar__new-chat-btn:hover {
  background-color: var(--accent-hover);
  transform: translateY(-1px);
}

.sidebar__new-chat-icon {
  font-size: 18px;
  font-weight: bold;
}

/* Navigation */
.sidebar__nav {
  flex: 1;
  padding: 8px 16px;
}

.sidebar__nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar__nav-item {
  width: 100%;
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
  text-align: left;
}

.sidebar--closed .sidebar__nav-item {
  justify-content: center;
  padding: 12px;
}

.sidebar__nav-item:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.sidebar__nav-item--active {
  background-color: var(--card-bg);
  color: var(--accent-color);
}

.sidebar__nav-icon {
  font-size: 18px;
  min-width: 20px;
  text-align: center;
}

.sidebar__nav-label {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar--closed .sidebar__nav-label {
  display: none;
}

/* Bottom Section */
.sidebar__bottom {
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

/* User Profile */
.sidebar__user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: var(--card-bg);
  margin-top: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.sidebar__user-profile:hover {
  background-color: var(--accent-hover);
}

.sidebar--closed .sidebar__user-profile {
  justify-content: center;
  padding: 12px;
}

.sidebar__user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.sidebar__user-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sidebar__user-avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.sidebar__user-info {
  flex: 1;
  min-width: 0;
}

.sidebar--closed .sidebar__user-info {
  display: none;
}

.sidebar__user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar__user-plan {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Logout Button */
.sidebar__logout-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
}

.sidebar__logout-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar--open {
    width: 100%;
    position: absolute;
    z-index: 1000;
    height: 100vh;
  }

  .sidebar--closed {
    display: none;
  }
}
