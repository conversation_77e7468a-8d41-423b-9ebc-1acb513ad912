import React, { useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import ChatInput from '../ChatInput/ChatInput';
import './ChatArea.css';

const ChatArea = ({ isSidebarOpen, onToggleSidebar }) => {
  const [messages, setMessages] = useState([]);
  const { user } = useAuth();

  const getUserName = () => {
    if (user?.firstName) {
      return user.firstName;
    } else if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'there';
  };

  const defaultPrompts = [
    {
      id: 1,
      icon: '🎵',
      title: 'Create a playlist',
      description: 'Help me create a music playlist for working'
    },
    {
      id: 2,
      icon: '💰',
      title: 'Investment advice',
      description: 'What are some good investment strategies?'
    },
    {
      id: 3,
      icon: '🔍',
      title: 'Research help',
      description: 'Help me research a topic thoroughly'
    },
    {
      id: 4,
      icon: '✍️',
      title: 'Writing assistant',
      description: 'Help me write and improve my content'
    }
  ];

  const handlePromptClick = (prompt) => {
    // Handle prompt click - would normally send to chat
    console.log('Prompt clicked:', prompt);
  };

  const handleSendMessage = (message) => {
    // Handle sending message - would normally send to API
    console.log('Message sent:', message);
    setMessages(prev => [...prev, { text: message, sender: 'user', timestamp: new Date() }]);
  };

  return (
    <div className="chat-area">
      {/* Header */}
      <div className="chat-area__header">
        <div className="chat-area__title">
          <h1>AI Chat</h1>
        </div>
        <button className="chat-area__share">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M4 12V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 6L12 2L8 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 2V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>

      {/* Main Content */}
      <div className="chat-area__content">
        {messages.length === 0 ? (
          <div className="chat-area__welcome">
            <div className="chat-area__greeting">
              <h2>Hey, {getUserName()}! What's up today?</h2>
            </div>

            <div className="chat-area__prompts">
              {defaultPrompts.map((prompt) => (
                <button
                  key={prompt.id}
                  className="chat-area__prompt-card"
                  onClick={() => handlePromptClick(prompt)}
                >
                  <div className="chat-area__prompt-icon">{prompt.icon}</div>
                  <div className="chat-area__prompt-content">
                    <div className="chat-area__prompt-title">{prompt.title}</div>
                    <div className="chat-area__prompt-description">{prompt.description}</div>
                  </div>
                  <div className="chat-area__prompt-add">+</div>
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div className="chat-area__messages">
            {messages.map((message, index) => (
              <div key={index} className={`chat-message chat-message--${message.sender}`}>
                <div className="chat-message__content">
                  {message.text}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Chat Input */}
      <div className="chat-area__input-container">
        <ChatInput onSendMessage={handleSendMessage} />
      </div>
    </div>
  );
};

export default ChatArea;
