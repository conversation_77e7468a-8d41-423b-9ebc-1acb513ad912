/* Profile Components Styles */
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #ffffff;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #3a3a3a;
}

.profile-header h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.profile-header p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.profile-section {
  background: #2a2a2a;
  border-radius: 12px;
  padding: 30px;
  border: 1px solid #3a3a3a;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  margin-top: 16px;
  color: #b0b0b0;
  font-size: 16px;
}

/* Form Styles */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.form-group input {
  background: #1a1a1a;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #fcd469;
  box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
}

.form-value {
  background: #1a1a1a;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 16px;
  color: #b0b0b0;
  font-size: 16px;
  min-height: 20px;
}

.form-hint {
  font-size: 12px;
  color: #888888;
  margin-top: 4px;
}

/* Button Styles */
.edit-btn, .save-btn, .cancel-btn, .change-password-btn, .upload-btn, .remove-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.edit-btn, .change-password-btn {
  background: #fcd469;
  color: #1a1a1a;
}

.edit-btn:hover, .change-password-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
}

.save-btn {
  background: #10b981;
  color: #ffffff;
}

.save-btn:hover:not(:disabled) {
  background: #059669;
}

.cancel-btn, .remove-btn {
  background: transparent;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.cancel-btn:hover:not(:disabled), .remove-btn:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.1);
}

.upload-btn {
  background: #fcd469;
  color: #1a1a1a;
  cursor: pointer;
  display: inline-block;
  text-align: center;
}

.upload-btn:hover {
  background: #f5c842;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

/* Error and Success Messages */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 12px;
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 16px;
}

.success-message {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 6px;
  padding: 12px;
  color: #10b981;
  font-size: 14px;
  margin-bottom: 16px;
}

/* Profile Picture Styles */
.profile-picture-section h2 {
  margin-bottom: 20px;
}

.profile-picture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.profile-picture {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid #3a3a3a;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-initials {
  width: 100%;
  height: 100%;
  background: #fcd469;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: 600;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #3a3a3a;
  border-top: 2px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.profile-picture-actions {
  display: flex;
  gap: 12px;
}

.profile-picture-hint {
  text-align: center;
  color: #888888;
  font-size: 14px;
  margin-top: 16px;
  max-width: 400px;
}

/* Password Change Styles */
.password-change-section .password-info {
  color: #b0b0b0;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.security-tips {
  margin-top: 20px;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.security-tips h4 {
  color: #fcd469;
  margin: 0 0 12px 0;
  font-size: 16px;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #b0b0b0;
}

.security-tips li {
  margin-bottom: 8px;
  font-size: 14px;
}

/* Credit Display Styles */
.credit-display-section .refresh-btn {
  background: transparent;
  border: 1px solid #3a3a3a;
  color: #fcd469;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.credit-display-section .refresh-btn:hover {
  background: rgba(252, 212, 105, 0.1);
  border-color: #fcd469;
}

.credit-summary {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.credit-card {
  background: linear-gradient(135deg, #fcd469 0%, #f5c842 100%);
  color: #1a1a1a;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(252, 212, 105, 0.2);
}

.credit-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.credit-amount {
  display: flex;
  flex-direction: column;
}

.credit-number {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

.credit-label {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.8;
}

.credit-icon {
  font-size: 48px;
}

.credit-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 4px;
}

.credit-info {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #3a3a3a;
}

.credit-info h4 {
  color: #fcd469;
  margin: 0 0 16px 0;
  font-size: 18px;
}

.credit-info ul {
  margin: 0;
  padding-left: 20px;
  color: #b0b0b0;
}

.credit-info li {
  margin-bottom: 8px;
  font-size: 14px;
}

/* Credit Transactions */
.credit-transactions {
  border-top: 1px solid #3a3a3a;
  padding-top: 20px;
}

.toggle-transactions-btn {
  background: transparent;
  border: 1px solid #3a3a3a;
  color: #ffffff;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.toggle-transactions-btn:hover {
  background: rgba(252, 212, 105, 0.1);
  border-color: #fcd469;
}

.arrow {
  transition: transform 0.3s ease;
}

.arrow.up {
  transform: rotate(180deg);
}

.transactions-list {
  margin-top: 20px;
}

.loading-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: #b0b0b0;
}

.transactions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.transaction-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.transaction-details {
  flex: 1;
}

.transaction-description {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4px;
}

.transaction-date {
  font-size: 12px;
  color: #888888;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 600;
}

.transaction-amount.positive {
  color: #10b981;
}

.transaction-amount.negative {
  color: #ef4444;
}

.transaction-amount.neutral {
  color: #fcd469;
}

.no-transactions {
  text-align: center;
  padding: 40px;
  color: #888888;
}

/* Plan Management Styles */
.plan-management-section .current-plan {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #1a1a1a;
  text-transform: uppercase;
}

.plan-badge.free {
  background: #6b7280;
}

.plan-badge.premium {
  background: #fcd469;
}

.plan-badge.enterprise {
  background: #8b5cf6;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.plan-card {
  background: #1a1a1a;
  border: 2px solid #3a3a3a;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
}

.plan-card:hover {
  border-color: #fcd469;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.plan-card.current {
  border-color: #fcd469;
  background: rgba(252, 212, 105, 0.05);
}

.plan-header {
  text-align: center;
  margin-bottom: 24px;
}

.plan-header h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.plan-price {
  font-size: 32px;
  font-weight: 700;
  color: #fcd469;
  margin-bottom: 4px;
}

.plan-credits {
  font-size: 14px;
  color: #b0b0b0;
}

.plan-features {
  margin-bottom: 24px;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: #b0b0b0;
  font-size: 14px;
}

.feature-check {
  color: #10b981;
  font-weight: 600;
  font-size: 16px;
}

.plan-action {
  margin-bottom: 16px;
}

.upgrade-btn, .current-plan-btn {
  width: 100%;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.upgrade-btn {
  color: #1a1a1a;
}

.upgrade-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.upgrade-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.current-plan-btn {
  background: #3a3a3a;
  color: #888888;
  cursor: not-allowed;
}

.current-plan-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #10b981;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.plan-info {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #3a3a3a;
}

.plan-info h4 {
  color: #fcd469;
  margin: 0 0 16px 0;
  font-size: 18px;
}

.benefits-comparison {
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
}

.benefit-item {
  padding: 16px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
  color: #b0b0b0;
  font-size: 14px;
}

.benefit-item strong {
  color: #ffffff;
}

.billing-info {
  border-top: 1px solid #3a3a3a;
  padding-top: 16px;
}

.billing-info p {
  margin: 0 0 8px 0;
  color: #888888;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 15px;
  }

  .profile-section {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .edit-actions, .form-actions {
    flex-direction: column;
  }

  .credit-summary {
    grid-template-columns: 1fr;
  }

  .credit-stats {
    grid-template-columns: 1fr;
  }

  .plans-grid {
    grid-template-columns: 1fr;
  }

  .profile-picture-actions {
    flex-direction: column;
    width: 100%;
  }
}
