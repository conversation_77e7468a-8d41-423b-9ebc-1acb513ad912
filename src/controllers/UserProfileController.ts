import { Request, Response } from 'express';
import { UserProfileService } from '../services/UserProfileService';
import { CreditService } from '../services/CreditService';
import { AuthService } from '../services/AuthService';
import { ResponseUtil } from '../utils/response';
import { AuthenticatedRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../config/logger';

export class UserProfileController {
  /**
   * Get user profile with credits
   */
  static getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { profile, credits } = await UserProfileService.getUserProfileWithCredits(req.user.userId);
    const creditSummary = await CreditService.getCreditSummary(req.user.userId);

    ResponseUtil.success(res, 'Profile retrieved successfully', {
      profile,
      credits: {
        current: credits,
        ...creditSummary,
      },
    });
  });

  /**
   * Update user profile
   */
  static updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { firstName, lastName, profilePicture } = req.body;

    const updatedProfile = await UserProfileService.updateUserProfile(req.user.userId, {
      firstName,
      lastName,
      profilePicture,
    });

    ResponseUtil.success(res, 'Profile updated successfully', { profile: updatedProfile });
  });

  /**
   * Change user password
   */
  static changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      ResponseUtil.validationError(res, 'Current password and new password are required');
      return;
    }

    await AuthService.changePassword(req.user.userId, currentPassword, newPassword);

    ResponseUtil.success(res, 'Password changed successfully');
  });

  /**
   * Get credit transactions
   */
  static getCreditTransactions = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const { transactions, total } = await CreditService.getCreditTransactions(
      req.user.userId,
      limit,
      offset
    );

    ResponseUtil.success(res, 'Credit transactions retrieved successfully', {
      transactions,
      total,
      limit,
      offset,
    });
  });

  /**
   * Get credit summary
   */
  static getCreditSummary = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const creditSummary = await CreditService.getCreditSummary(req.user.userId);

    ResponseUtil.success(res, 'Credit summary retrieved successfully', creditSummary);
  });

  /**
   * Update user plan
   */
  static updatePlan = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    const { plan } = req.body;

    if (!plan || !['FREE', 'PREMIUM', 'ENTERPRISE'].includes(plan)) {
      ResponseUtil.validationError(res, 'Valid plan is required (FREE, PREMIUM, ENTERPRISE)');
      return;
    }

    const updatedProfile = await UserProfileService.updateUserPlan(req.user.userId, plan);

    ResponseUtil.success(res, 'Plan updated successfully', { profile: updatedProfile });
  });

  /**
   * Upload profile picture (placeholder for file upload)
   */
  static uploadProfilePicture = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    // This is a placeholder implementation
    // In a real application, you would handle file upload here
    const { profilePictureUrl } = req.body;

    if (!profilePictureUrl) {
      ResponseUtil.validationError(res, 'Profile picture URL is required');
      return;
    }

    const updatedProfile = await UserProfileService.updateUserProfile(req.user.userId, {
      profilePicture: profilePictureUrl,
    });

    ResponseUtil.success(res, 'Profile picture updated successfully', { profile: updatedProfile });
  });
}
